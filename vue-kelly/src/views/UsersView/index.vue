<!--
 * @Author: <NAME_EMAIL>
 * @Date: 2025-08-03 22:49:32
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-08-03 23:03:25
 * @FilePath: /vue-kelly/src/views/UsersView/index.vue
 * @Description: 用户登录页面
-->
<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h2>用户登录</h2>
        <p>请输入您的账号和密码</p>
      </div>

      <a-form
        :model="loginForm"
        :rules="rules"
        @finish="handleLogin"
        @finishFailed="handleLoginFailed"
        layout="vertical"
        class="login-form"
      >
        <a-form-item label="账号" name="username" has-feedback>
          <a-input
            v-model:value="loginForm.username"
            placeholder="请输入账号"
            size="large"
            :prefix="h(UserOutlined)"
          />
        </a-form-item>

        <a-form-item label="密码" name="password" has-feedback>
          <a-input-password
            v-model:value="loginForm.password"
            placeholder="请输入密码"
            size="large"
            :prefix="h(LockOutlined)"
          />
        </a-form-item>

        <a-form-item>
          <a-checkbox v-model:checked="loginForm.remember"> 记住我 </a-checkbox>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" html-type="submit" size="large" :loading="loading" block>
            登录
          </a-button>
        </a-form-item>
      </a-form>

      <!-- 登录状态显示 -->
      <div v-if="loginResult" class="login-result">
        <a-alert
          :message="loginResult.success ? '登录成功' : '登录失败'"
          :description="loginResult.message"
          :type="loginResult.success ? 'success' : 'error'"
          show-icon
          closable
          @close="loginResult = null"
        />
      </div>

      <!-- Mock 数据说明 -->
      <div class="mock-info">
        <a-divider>测试账号</a-divider>
        <div class="test-accounts">
          <div
            v-for="account in testAccounts"
            :key="account.username"
            class="account-item"
            @click="fillAccount(account)"
          >
            <span class="account-role">{{ account.name }}</span>
            <span class="account-info">{{ account.username }} / {{ account.password }}</span>
          </div>
        </div>
        <p class="text-gray-500 text-xs mt-2">点击账号可快速填入</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'
import { mockLogin, getTestAccounts } from '@/api/auth'
import type { Rule } from 'ant-design-vue/es/form'

// 用户状态管理
const userStore = useUserStore()

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
  remember: false,
})

// 加载状态
const loading = ref(false)

// 登录结果
const loginResult = ref<{
  success: boolean
  message: string
} | null>(null)

// 表单验证规则
const rules: Record<string, Rule[]> = {
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 20, message: '账号长度应为3-20个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20个字符', trigger: 'blur' },
  ],
}

// 获取测试账号信息
const testAccounts = getTestAccounts()

// 快速填入账号信息
const fillAccount = (account: { username: string; password: string }) => {
  loginForm.username = account.username
  loginForm.password = account.password
  message.info(`已填入 ${account.username} 的登录信息`)
}

// 处理登录
const handleLogin = async (values: typeof loginForm) => {
  loading.value = true
  loginResult.value = null

  try {
    const result = await mockLogin({
      username: values.username,
      password: values.password,
      remember: values.remember,
    })

    if (result.success) {
      // 保存用户信息到状态管理
      userStore.setUser(result.data)

      // 显示成功消息
      message.success(result.message)

      // 设置登录结果
      loginResult.value = {
        success: true,
        message: `欢迎回来，${result.data?.name}！`,
      }

      // 如果选择了记住我，可以在这里保存到 localStorage
      if (values.remember) {
        localStorage.setItem('rememberedUser', values.username)
      }
    } else {
      loginResult.value = {
        success: false,
        message: result.message,
      }
      message.error(result.message)
    }
  } catch (error) {
    console.error('登录错误:', error)
    loginResult.value = {
      success: false,
      message: '网络错误，请稍后重试',
    }
    message.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 处理登录失败（表单验证失败）
const handleLoginFailed = (errorInfo: {
  values: Record<string, unknown>
  errorFields: Array<{ name: string[]; errors: string[] }>
}) => {
  console.log('表单验证失败:', errorInfo)
  message.warning('请检查输入信息')
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.login-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.login-form {
  margin-bottom: 20px;
}

.login-result {
  margin-bottom: 20px;
}

.mock-info {
  text-align: center;
}

.mock-info p {
  margin: 0;
  line-height: 1.6;
}

.test-accounts {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
}

.account-item {
  display: flex;
  flex-direction: column;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.account-item:hover {
  background: #e9ecef;
  border-color: #1890ff;
  transform: translateY(-1px);
}

.account-role {
  font-size: 12px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 2px;
}

.account-info {
  font-size: 11px;
  color: #666;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
    margin: 10px;
  }

  .login-header h2 {
    font-size: 20px;
  }
}
</style>
