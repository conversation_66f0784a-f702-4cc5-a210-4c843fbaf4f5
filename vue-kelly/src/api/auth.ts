/*
 * @Author: <NAME_EMAIL>
 * @Date: 2025-08-03 23:05:00
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-08-03 23:05:00
 * @FilePath: /vue-kelly/src/api/auth.ts
 * @Description: 认证相关 API
 */

import type { UserInfo, LoginResponse } from '@/stores/user'

// 登录请求参数接口
export interface LoginParams {
  username: string
  password: string
  remember?: boolean
}

// Mock 用户数据
const mockUsers = [
  { 
    username: 'admin', 
    password: 'admin123', 
    role: 'admin', 
    name: '管理员',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin'
  },
  { 
    username: 'user', 
    password: 'user123', 
    role: 'user', 
    name: '普通用户',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user'
  },
  { 
    username: 'guest', 
    password: 'guest123', 
    role: 'guest', 
    name: '访客用户',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=guest'
  }
]

// 模拟网络延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

/**
 * Mock 登录 API
 * @param params 登录参数
 * @returns 登录响应
 */
export const mockLogin = async (params: LoginParams): Promise<LoginResponse> => {
  // 模拟网络延迟 800-1500ms
  const delayTime = Math.random() * 700 + 800
  await delay(delayTime)
  
  const { username, password } = params
  
  // 查找用户
  const user = mockUsers.find(u => u.username === username && u.password === password)
  
  if (user) {
    const userInfo: UserInfo = {
      id: Date.now(),
      username: user.username,
      name: user.name,
      role: user.role,
      token: `mock_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      avatar: user.avatar
    }
    
    return {
      success: true,
      data: userInfo,
      message: '登录成功'
    }
  } else {
    // 模拟不同的错误情况
    const errorMessages = [
      '账号或密码错误',
      '用户不存在',
      '密码错误，请重试'
    ]
    
    return {
      success: false,
      data: null,
      message: errorMessages[Math.floor(Math.random() * errorMessages.length)]
    }
  }
}

/**
 * Mock 登出 API
 * @returns 登出响应
 */
export const mockLogout = async (): Promise<{ success: boolean; message: string }> => {
  await delay(500)
  
  return {
    success: true,
    message: '登出成功'
  }
}

/**
 * Mock 获取用户信息 API
 * @param token 用户token
 * @returns 用户信息响应
 */
export const mockGetUserInfo = async (token: string): Promise<LoginResponse> => {
  await delay(300)
  
  // 简单的 token 验证
  if (!token || !token.startsWith('mock_token_')) {
    return {
      success: false,
      data: null,
      message: 'Token 无效'
    }
  }
  
  // 从 token 中提取用户信息（这里只是模拟）
  const mockUser = mockUsers[0] // 默认返回管理员信息
  
  const userInfo: UserInfo = {
    id: Date.now(),
    username: mockUser.username,
    name: mockUser.name,
    role: mockUser.role,
    token: token,
    avatar: mockUser.avatar
  }
  
  return {
    success: true,
    data: userInfo,
    message: '获取用户信息成功'
  }
}

/**
 * 获取所有测试账号信息
 * @returns 测试账号列表
 */
export const getTestAccounts = () => {
  return mockUsers.map(user => ({
    username: user.username,
    password: user.password,
    role: user.role,
    name: user.name
  }))
}
